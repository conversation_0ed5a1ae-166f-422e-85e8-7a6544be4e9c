<?php
/**
 * User Login Page
 * 
 * Handles user authentication with email or phone number
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';
$identifier_value = '';
$password_value = '';

// Redirect if already logged in
if ($userAuth->isAuthenticated()) {
    header("Location: dashboard.php");
    exit();
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    // Preserve form values for redisplay
    $identifier_value = isset($_POST['identifier']) ? htmlspecialchars($_POST['identifier']) : '';

    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $identifier = $security->sanitizeInput($_POST['identifier'], 'text');
        $password = $_POST['password'];

        if (empty($identifier) || empty($password)) {
            $error = "Please enter both email/phone and password.";
        } else {
            $result = $userAuth->authenticateUser($identifier, $password);

            if ($result['success']) {
                // Successful authentication - redirect to dashboard
                if ($result['requires_password_change']) {
                    header("Location: change_password.php?first_login=1");
                } else {
                    header("Location: dashboard.php");
                }
                exit();
            } else {
                $error = $result['message'];
            }
        }
    }
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Member Portal';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Login - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6366f1;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 48px 40px;
            width: 100%;
            max-width: 440px;
            box-shadow: var(--shadow-xl);
            position: relative;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-xl);
        }

        .login-logo img {
            max-width: 60px;
            max-height: 60px;
            object-fit: contain;
            border-radius: 12px;
        }

        .login-logo i {
            font-size: 32px;
            color: white;
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .login-subtitle {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .form-floating {
            margin-bottom: 24px;
            position: relative;
        }

        .form-floating input {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-floating input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            background: white;
        }

        .form-floating label {
            color: #6b7280;
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 24px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-login.loading {
            opacity: 0.8;
            pointer-events: none;
        }

        .quick-access {
            text-align: center;
            margin-bottom: 24px;
        }

        .quick-access-title {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .quick-links {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .quick-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(79, 70, 229, 0.1);
            color: var(--primary-color);
            text-decoration: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(79, 70, 229, 0.2);
        }

        .quick-link:hover {
            background: rgba(79, 70, 229, 0.15);
            color: var(--primary-hover);
            text-decoration: none;
            transform: translateY(-1px);
        }

        .forgot-link {
            text-align: center;
            margin-top: 16px;
        }

        .forgot-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .forgot-link a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .help-text {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 8px;
            text-align: center;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 32px 24px;
                margin: 16px;
            }

            .quick-links {
                flex-direction: column;
                align-items: center;
            }

            .quick-link {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">
                <?php
                // Use the proper logo system
                $headerLogo = get_site_setting('header_logo', '');
                $mainLogo = get_site_setting('main_logo', '');
                $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

                if (!empty($logoToUse) && file_exists(__DIR__ . '/../' . $logoToUse)):
                ?>
                    <img src="<?php echo '../' . htmlspecialchars($logoToUse); ?>" alt="<?php echo htmlspecialchars(get_organization_name()); ?> Logo">
                <?php elseif (file_exists(__DIR__ . '/../assets/images/church-logo-simple.svg')): ?>
                    <img src="../assets/images/church-logo-simple.svg" alt="<?php echo htmlspecialchars(get_organization_name()); ?> Logo">
                <?php else: ?>
                    <i class="fas fa-users"></i>
                <?php endif; ?>
            </div>
            <h1 class="login-title">
                <i class="fas fa-user-circle me-2"></i>Member Portal
            </h1>
            <p class="login-subtitle"><?php echo htmlspecialchars(get_organization_name()); ?></p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="loginForm">
            <?php echo $security->generateCSRFInput(); ?>
            <input type="hidden" name="login" value="1">

            <div class="form-floating">
                <input type="text"
                       class="form-control"
                       id="identifier"
                       name="identifier"
                       value="<?php echo $identifier_value; ?>"
                       placeholder="Email or Phone Number"
                       required
                       autocomplete="username">
                <label for="identifier">
                    <i class="fas fa-envelope me-2"></i>Email or Phone Number
                </label>
                <div class="help-text">
                    You can log in using either your email address or phone number
                </div>
            </div>

            <div class="form-floating">
                <input type="password"
                       class="form-control"
                       id="password"
                       name="password"
                       placeholder="Password"
                       required
                       autocomplete="current-password">
                <label for="password">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
            </div>

            <button type="submit" name="login" class="btn-login" id="loginBtn">
                <i class="fas fa-sign-in-alt me-2"></i>
                Sign In
            </button>
        </form>

        <div class="forgot-link">
            <a href="forgot_password.php">
                <i class="fas fa-key me-1"></i>
                Forgot Your Password?
            </a>
        </div>

        <div class="quick-access">
            <div class="quick-access-title">Quick Access</div>
            <div class="quick-links">
                <a href="../register.php" class="quick-link">
                    <i class="fas fa-user-plus"></i>
                    Register
                </a>
                <a href="../index.php" class="quick-link">
                    <i class="fas fa-home"></i>
                    Homepage
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced login form interactions
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const identifierInput = document.getElementById('identifier');
            const passwordInput = document.getElementById('password');

            // Add loading state on form submission
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    // Just add loading state, don't prevent submission
                    loginBtn.classList.add('loading');
                    loginBtn.disabled = true;
                    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';

                    // Re-enable button after 10 seconds as fallback
                    setTimeout(() => {
                        loginBtn.classList.remove('loading');
                        loginBtn.disabled = false;
                        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Sign In';
                    }, 10000);
                });
            }

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('.form-floating input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // Auto-focus identifier field
            if (identifierInput) {
                identifierInput.focus();
            }

            // Add ripple effect to buttons
            const buttons = document.querySelectorAll('.btn-login, .quick-link');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Alt + H for homepage
                if (e.altKey && e.key === 'h') {
                    e.preventDefault();
                    window.location.href = '../index.php';
                }

                // Alt + R for register
                if (e.altKey && e.key === 'r') {
                    e.preventDefault();
                    window.location.href = '../register.php';
                }
            });
        });

        // Add CSS for ripple effects
        const style = document.createElement('style');
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .quick-link {
                position: relative;
                overflow: hidden;
            }

            .btn-login {
                position: relative;
                overflow: hidden;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
