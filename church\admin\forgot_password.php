<?php
session_start();

// If already logged in, redirect to admin dashboard
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Debug: Log email settings availability
error_log("Forgot password - Email settings available: " . (empty($emailSettings) ? 'NO' : 'YES'));
if (!empty($emailSettings)) {
    error_log("Email settings keys: " . implode(', ', array_keys($emailSettings)));
}

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Process password reset request
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $username = $security->sanitizeInput($_POST['username'], 'text');
        
        // Check if username exists
        $stmt = $conn->prepare("SELECT id, email, full_name FROM admins WHERE username = ?");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin) {
            // Generate password reset token
            $token = $security->generateToken(32);
            
            // Set expiration time with a longer window (4 hours) and ensure timezone consistency
            $expires = date('Y-m-d H:i:s', strtotime('+4 hours'));
            
            // Log token generation
            error_log("Generated token: " . $token . " with expiration: " . $expires);
            
            // Save token to database
            $stmt = $conn->prepare("
                UPDATE admins 
                SET password_reset_token = ?, password_reset_expires = ? 
                WHERE id = ?
            ");
            $stmt->execute([$token, $expires, $admin['id']]);
            
            // Build reset URL - use configured ADMIN_URL
            $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? "https" : "http";
            $host = $_SERVER['HTTP_HOST'];
            
            // Use the defined ADMIN_URL constant instead of hardcoded path
            $resetUrl = ADMIN_URL . "/reset_password.php?token=" . urlencode($token);
            
            // For debugging
            error_log("Reset URL: " . $resetUrl);
            
            // Send password reset email
            error_log("Attempting to send password reset email to: " . $admin['email']);
            $emailSent = sendPasswordResetEmail($admin, $resetUrl);

            if ($emailSent) {
                $success = "Password reset instructions have been sent to your email address.";
                error_log("Password reset email sent successfully to: " . $admin['email']);

                // Log password reset request
                $security->logSecurityEvent('Password reset requested', [
                    'admin_id' => $admin['id'],
                    'username' => $username,
                    'expires' => $expires
                ]);
            } else {
                error_log("Failed to send password reset email to: " . $admin['email']);

                // Check for specific email error
                global $last_email_error;
                if ($last_email_error) {
                    error_log("Email error details: " . $last_email_error);
                    $error = "Failed to send password reset email: " . $last_email_error;
                } else {
                    $error = "Failed to send password reset email. Please try again or contact an administrator.";
                }
            }
        } else {
            // For security reasons, show the same message even if the username doesn't exist
            // This prevents username enumeration
            $success = "If your username exists in our system, password reset instructions have been sent to your email address.";
            
            // Log attempted reset for non-existent user
            $security->logSecurityEvent('Password reset attempted for non-existent user', [
                'username' => $username
            ]);
        }
    }
}

/**
 * Send password reset email
 * 
 * @param array $admin Admin data
 * @param string $resetUrl Reset URL
 * @return bool True if email sent successfully
 */
function sendPasswordResetEmail($admin, $resetUrl) {
    global $pdo, $emailSettings;

    error_log("sendPasswordResetEmail called for: " . $admin['email']);

    // Check if email settings are available
    if (empty($emailSettings)) {
        error_log("Email settings are empty in sendPasswordResetEmail");
        return false;
    }

    // Get email template
    $stmt = $pdo->prepare("
        SELECT subject, content
        FROM email_templates
        WHERE template_name LIKE '%password%reset%'
        LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch();

    error_log("Email template found: " . ($template ? 'YES' : 'NO'));
    
    if (!$template) {
        // Use default template if none found
        $subject = "Password Reset - " . get_organization_name() . " Admin";
        $content = "
            <html>
            <body>
                <h2>Password Reset Request</h2>
                <p>Dear {full_name},</p>
                <p>We received a request to reset your password for the " . htmlspecialchars(get_organization_name()) . " Admin Portal.</p>
                <p>To reset your password, please click the link below:</p>
                <p><a href='{reset_url}'>Reset Password</a></p>
                <p>This link will expire in 1 hour.</p>
                <p>If you did not request a password reset, please ignore this email or contact an administrator.</p>
                <p>Thank you,<br>" . htmlspecialchars(get_organization_name()) . " Admin Team</p>
            </body>
            </html>
        ";
    } else {
        $subject = $template['subject'];
        $content = $template['content'];
    }
    
    // Replace placeholders
    $subject = str_replace('{full_name}', $admin['full_name'], $subject);
    
    // Make sure the reset URL is properly encoded for HTML
    $safeResetUrl = htmlspecialchars($resetUrl, ENT_QUOTES, 'UTF-8');
    
    $content = str_replace(
        ['{full_name}', '{reset_url}', '{expires}'],
        [$admin['full_name'], $safeResetUrl, '1 hour'],
        $content
    );
    
    // Send email using the global function
    error_log("Calling sendEmail with subject: " . $subject);
    error_log("Email content length: " . strlen($content));

    $result = sendEmail(
        $admin['email'],
        $admin['full_name'],
        $subject,
        $content,
        true
    );

    error_log("sendEmail result: " . ($result ? 'SUCCESS' : 'FAILED'));
    return $result;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6366f1;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .forgot-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 48px 40px;
            width: 100%;
            max-width: 440px;
            box-shadow: var(--shadow-xl);
            position: relative;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .forgot-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .forgot-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--warning-color), #fb923c);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-xl);
        }

        .forgot-icon img {
            max-width: 60px;
            max-height: 60px;
            object-fit: contain;
            border-radius: 12px;
        }

        .forgot-icon i {
            font-size: 32px;
            color: white;
        }

        .forgot-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .forgot-subtitle {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .form-floating {
            margin-bottom: 24px;
            position: relative;
        }

        .form-floating input {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-floating input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            background: white;
        }

        .form-floating label {
            color: #6b7280;
            font-weight: 500;
        }

        .btn-reset {
            background: linear-gradient(135deg, var(--warning-color), #fb923c);
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-xl);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 24px;
        }

        .back-link:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .info-box {
            background: rgba(79, 70, 229, 0.05);
            border: 1px solid rgba(79, 70, 229, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .info-box p {
            margin: 0;
            color: #6b7280;
            line-height: 1.6;
        }

        @media (max-width: 480px) {
            .forgot-container {
                padding: 32px 24px;
                margin: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-header">
            <div class="forgot-icon">
                <a href="../../index.html" title="Go to Homepage">
                <?php
                // Use the proper logo system
                $headerLogo = get_site_setting('header_logo', '');
                $mainLogo = get_site_setting('main_logo', '');
                $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

                if (!empty($logoToUse) && file_exists(__DIR__ . '/../' . $logoToUse)):
                ?>
                    <img src="<?php echo '../' . htmlspecialchars($logoToUse); ?>" alt="<?php echo htmlspecialchars(get_organization_name()); ?> Logo">
                <?php elseif (file_exists(__DIR__ . '/../assets/images/church-logo-simple.svg')): ?>
                    <img src="../assets/images/church-logo-simple.svg" alt="<?php echo htmlspecialchars(get_organization_name()); ?> Logo">
                <?php else: ?>
                    <i class="fas fa-key"></i>
                <?php endif; ?>
                </a>
            </div>
            <h1 class="forgot-title">Reset Password</h1>
            <p class="forgot-subtitle"><?php echo htmlspecialchars(get_organization_name()); ?></p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success; ?>
            </div>
            <div class="text-center">
                <a href="login.php" class="btn btn-primary-custom">
                    <i class="fas fa-arrow-left me-2"></i>
                    Return to Login
                </a>
            </div>
        <?php else: ?>
            <div class="info-box">
                <p>
                    <i class="fas fa-info-circle me-2"></i>
                    Enter your username below and we'll send you instructions to reset your password.
                </p>
            </div>

            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="resetForm">
                <?php echo $security->generateCSRFInput(); ?>

                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                    <label for="username">
                        <i class="fas fa-user me-2"></i>Username
                    </label>
                </div>

                <button type="submit" class="btn-reset" id="resetBtn">
                    <i class="fas fa-paper-plane me-2"></i>
                    Send Reset Instructions
                </button>

                <div class="text-center">
                    <a href="login.php" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Login
                    </a>
                </div>
            </form>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('resetForm');
            const resetBtn = document.getElementById('resetBtn');
            const usernameInput = document.getElementById('username');

            // Auto-focus username field
            if (usernameInput) {
                usernameInput.focus();
            }

            // Add loading state on form submission
            if (resetForm) {
                resetForm.addEventListener('submit', function(e) {
                    resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                    resetBtn.disabled = true;

                    // Re-enable button after 10 seconds as fallback
                    setTimeout(() => {
                        resetBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Reset Instructions';
                        resetBtn.disabled = false;
                    }, 10000);
                });
            }

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('.form-floating input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>